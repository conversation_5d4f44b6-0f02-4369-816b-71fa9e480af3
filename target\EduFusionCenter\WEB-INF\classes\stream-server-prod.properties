# 生产环境流服务器配置
# Production Environment Stream Server Configuration

# 继承默认配置，只覆盖特定设置
stream.server.environment=production

# 生产环境路径（通常通过环境变量或系统属性指定）
stream.server.project.root=

# 生产环境日志较简洁
stream.server.log.level=INFO
stream.server.log.detailed=false

# 生产环境健康检查配置
stream.server.health.check.max.retries=10
stream.server.health.check.retry.interval=3000

# 生产环境超时设置
stream.server.connection.timeout=20000
stream.server.read.timeout=45000
