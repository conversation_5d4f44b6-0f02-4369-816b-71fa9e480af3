<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!-- 页面标题 -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
    <h1 class="h2">
        <i class="bi bi-diagram-3 me-2 text-primary"></i>产线详情
    </h1>
    <div class="btn-toolbar">
        <div class="btn-group me-2">
            <a href="${pageContext.request.contextPath}/production/list" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
</div>

<!-- 产线详情卡片 -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <c:choose>
                    <c:when test="${not empty productionLine}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">产线ID:</label>
                                    <p class="form-control-plaintext">${productionLine.id}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">产线名称:</label>
                                    <p class="form-control-plaintext">${productionLine.name}</p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">运行状态:</label>
                                    <p class="form-control-plaintext">
                                        <c:choose>
                                            <c:when test="${productionLine.status == '运行中'}">
                                                <span class="badge bg-success">${productionLine.status}</span>
                                            </c:when>
                                            <c:when test="${productionLine.status == '维护中'}">
                                                <span class="badge bg-warning">${productionLine.status}</span>
                                            </c:when>
                                            <c:when test="${productionLine.status == '停止'}">
                                                <span class="badge bg-danger">${productionLine.status}</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge bg-secondary">${productionLine.status}</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">最后更新时间:</label>
                                    <p class="form-control-plaintext">${productionLine.lastUpdated}</p>
                                </div>
                            </div>
                        </div>
                    </c:when>
                    <c:otherwise>
                        <div class="alert alert-warning" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            未找到产线信息
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </div>
    
    <!-- 操作面板 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>操作面板
                </h5>
            </div>
            <div class="card-body">
                <c:if test="${not empty productionLine}">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" onclick="editProductionLine(${productionLine.id})">
                            <i class="bi bi-pencil me-1"></i>编辑产线
                        </button>
                        <button type="button" class="btn btn-danger" onclick="deleteProductionLine(${productionLine.id})">
                            <i class="bi bi-trash me-1"></i>删除产线
                        </button>
                    </div>
                </c:if>
            </div>
        </div>
        
        <!-- 状态统计 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart me-2"></i>状态统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success">3</h4>
                            <small class="text-muted">运行中</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-warning">2</h4>
                            <small class="text-muted">维护中</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-danger">1</h4>
                        <small class="text-muted">停止</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editProductionLine(id) {
    // 跳转到编辑页面或打开编辑模态框
    window.location.href = '${pageContext.request.contextPath}/production/list';
}

function deleteProductionLine(id) {
    if (confirm('确定要删除这个产线吗？')) {
        $.ajax({
            url: '${pageContext.request.contextPath}/production/delete/' + id,
            type: 'POST',
            success: function(response) {
                if (response === 'success') {
                    alert('产线删除成功');
                    window.location.href = '${pageContext.request.contextPath}/production/list';
                } else {
                    alert('删除失败，请稍后重试');
                }
            },
            error: function() {
                alert('网络错误，请稍后重试');
            }
        });
    }
}
</script>
