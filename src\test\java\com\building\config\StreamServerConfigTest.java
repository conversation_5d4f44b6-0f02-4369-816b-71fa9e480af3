package com.building.config;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 流服务器配置测试类
 */
public class StreamServerConfigTest {
    
    private StreamServerConfig config;
    
    @Before
    public void setUp() {
        config = StreamServerConfig.getInstance();
    }
    
    @Test
    public void testDefaultConfiguration() {
        // 测试默认配置值
        assertEquals("http://127.0.0.1:3001", config.getServerUrl());
        assertEquals(15000, config.getConnectionTimeout());
        assertEquals(30000, config.getReadTimeout());
        assertEquals("node", config.getNodeCommand());
        assertEquals("stream-server.js", config.getScriptName());
        assertTrue(config.isHealthCheckEnabled());
        assertEquals(15, config.getHealthCheckMaxRetries());
        assertEquals(2000, config.getHealthCheckRetryInterval());
    }
    
    @Test
    public void testScriptSearchPaths() {
        // 测试脚本搜索路径
        assertNotNull(config.getScriptSearchPaths());
        assertFalse(config.getScriptSearchPaths().isEmpty());
        assertTrue(config.getScriptSearchPaths().contains("."));
    }
    
    @Test
    public void testProjectMarkers() {
        // 测试项目标识文件
        assertNotNull(config.getProjectMarkers());
        assertFalse(config.getProjectMarkers().isEmpty());
        assertTrue(config.getProjectMarkers().contains("package.json"));
        assertTrue(config.getProjectMarkers().contains("pom.xml"));
    }
    
    @Test
    public void testFallbackPaths() {
        // 测试备用路径
        assertNotNull(config.getFallbackPaths());
        assertFalse(config.getFallbackPaths().isEmpty());
    }
    
    @Test
    public void testSystemPropertyOverride() {
        // 测试系统属性覆盖
        String originalUrl = config.getServerUrl();
        
        // 设置系统属性
        System.setProperty("stream.server.url", "http://test:8080");
        
        // 重新加载配置
        config.reload();
        
        // 验证覆盖生效
        assertEquals("http://test:8080", config.getServerUrl());
        
        // 清理系统属性
        System.clearProperty("stream.server.url");
        config.reload();
        
        // 验证恢复默认值
        assertEquals(originalUrl, config.getServerUrl());
    }
    
    @Test
    public void testEnvironmentVariableSupport() {
        // 测试环境变量支持（这里只能测试逻辑，无法实际设置环境变量）
        String projectRoot = config.getProjectRoot();
        assertNotNull(projectRoot); // 应该返回非null值（可能是空字符串）
    }
}
