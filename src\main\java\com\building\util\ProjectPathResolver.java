package com.building.util;

import com.building.config.StreamServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目路径解析工具类
 * 负责智能查找项目根目录和脚本文件
 */
public class ProjectPathResolver {
    private static final Logger logger = LoggerFactory.getLogger(ProjectPathResolver.class);
    
    private final StreamServerConfig config;
    
    public ProjectPathResolver() {
        this.config = StreamServerConfig.getInstance();
    }
    
    /**
     * 查找项目根目录
     * @return 项目根目录路径，如果找不到则返回null
     */
    public String findProjectRoot() {
        logger.debug("开始查找项目根目录...");
        
        // 1. 检查配置文件中指定的路径
        String configuredRoot = config.getProjectRoot();
        if (!configuredRoot.isEmpty()) {
            File rootDir = new File(configuredRoot);
            if (rootDir.exists() && rootDir.isDirectory()) {
                logger.info("使用配置文件指定的项目根目录: {}", configuredRoot);
                return rootDir.getAbsolutePath();
            } else {
                logger.warn("配置文件指定的项目根目录不存在: {}", configuredRoot);
            }
        }
        
        // 2. 基于项目标识文件查找
        String rootByMarkers = findRootByProjectMarkers();
        if (rootByMarkers != null) {
            logger.info("通过项目标识文件找到项目根目录: {}", rootByMarkers);
            return rootByMarkers;
        }
        
        // 3. 基于类路径查找
        String rootByClasspath = findRootByClasspath();
        if (rootByClasspath != null) {
            logger.info("通过类路径找到项目根目录: {}", rootByClasspath);
            return rootByClasspath;
        }
        
        // 4. 使用备用路径
        String rootByFallback = findRootByFallbackPaths();
        if (rootByFallback != null) {
            logger.info("通过备用路径找到项目根目录: {}", rootByFallback);
            return rootByFallback;
        }

        // 5. 向后兼容：检查硬编码的已知路径
        String rootByLegacyPath = findRootByLegacyPaths();
        if (rootByLegacyPath != null) {
            logger.warn("通过向后兼容的硬编码路径找到项目根目录: {}", rootByLegacyPath);
            return rootByLegacyPath;
        }

        // 6. 最后使用当前工作目录
        String currentDir = System.getProperty("user.dir");
        logger.warn("无法找到合适的项目根目录，使用当前工作目录: {}", currentDir);
        return currentDir;
    }
    
    /**
     * 查找脚本文件
     * @param projectRoot 项目根目录
     * @return 脚本文件的完整路径，如果找不到则返回null
     */
    public String findScriptFile(String projectRoot) {
        if (projectRoot == null) {
            logger.error("项目根目录为null，无法查找脚本文件");
            return null;
        }
        
        String scriptName = config.getScriptName();
        List<String> searchPaths = config.getScriptSearchPaths();
        
        logger.debug("在项目根目录 {} 中查找脚本文件 {}", projectRoot, scriptName);
        
        for (String searchPath : searchPaths) {
            searchPath = searchPath.trim();
            File searchDir = searchPath.equals(".") ? 
                new File(projectRoot) : 
                new File(projectRoot, searchPath);
                
            File scriptFile = new File(searchDir, scriptName);
            logger.debug("检查脚本文件: {}", scriptFile.getAbsolutePath());
            
            if (scriptFile.exists() && scriptFile.isFile()) {
                logger.info("找到脚本文件: {}", scriptFile.getAbsolutePath());
                return scriptFile.getAbsolutePath();
            }
        }
        
        logger.error("在所有搜索路径中都未找到脚本文件 {}", scriptName);
        return null;
    }
    
    /**
     * 基于项目标识文件查找根目录
     */
    private String findRootByProjectMarkers() {
        List<String> markers = config.getProjectMarkers();
        
        // 从类路径开始向上查找
        try {
            URL classUrl = this.getClass().getProtectionDomain().getCodeSource().getLocation();
            Path currentPath = Paths.get(classUrl.toURI()).getParent();
            
            while (currentPath != null) {
                for (String marker : markers) {
                    marker = marker.trim();
                    File markerFile = currentPath.resolve(marker).toFile();
                    if (markerFile.exists()) {
                        logger.debug("找到项目标识文件: {}", markerFile.getAbsolutePath());
                        return currentPath.toString();
                    }
                }
                currentPath = currentPath.getParent();
            }
        } catch (Exception e) {
            logger.debug("通过类路径查找项目标识文件失败: {}", e.getMessage());
        }
        
        // 从当前工作目录开始向上查找
        File currentDir = new File(System.getProperty("user.dir"));
        while (currentDir != null) {
            for (String marker : markers) {
                marker = marker.trim();
                File markerFile = new File(currentDir, marker);
                if (markerFile.exists()) {
                    logger.debug("在工作目录中找到项目标识文件: {}", markerFile.getAbsolutePath());
                    return currentDir.getAbsolutePath();
                }
            }
            currentDir = currentDir.getParentFile();
        }
        
        return null;
    }
    
    /**
     * 基于类路径查找根目录
     */
    private String findRootByClasspath() {
        try {
            URL classUrl = this.getClass().getProtectionDomain().getCodeSource().getLocation();
            Path classPath = Paths.get(classUrl.toURI());
            
            logger.debug("类路径: {}", classPath);
            
            // 从类路径向上查找，直到找到包含脚本文件的目录
            Path currentPath = classPath.getParent();
            String scriptName = config.getScriptName();
            
            while (currentPath != null) {
                File scriptFile = currentPath.resolve(scriptName).toFile();
                if (scriptFile.exists()) {
                    return currentPath.toString();
                }
                currentPath = currentPath.getParent();
            }
        } catch (Exception e) {
            logger.debug("通过类路径查找失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 使用备用路径查找
     */
    private String findRootByFallbackPaths() {
        List<String> fallbackPaths = config.getFallbackPaths();
        String scriptName = config.getScriptName();
        
        for (String path : fallbackPaths) {
            path = path.trim();
            if (path.isEmpty()) continue;
            
            File dir = new File(path);
            if (dir.exists() && dir.isDirectory()) {
                File scriptFile = new File(dir, scriptName);
                if (scriptFile.exists()) {
                    logger.debug("在备用路径中找到脚本文件: {}", scriptFile.getAbsolutePath());
                    return dir.getAbsolutePath();
                }
            }
        }
        
        return null;
    }

    /**
     * 向后兼容：使用硬编码的已知路径查找
     */
    private String findRootByLegacyPaths() {
        // 这些是为了向后兼容而保留的硬编码路径
        String[] legacyPaths = {
            "C:\\Users\\<USER>\\Desktop\\EduFusionCenter",
            "C:\\EduFusionCenter",
            "D:\\EduFusionCenter"
        };

        String scriptName = config.getScriptName();

        for (String path : legacyPaths) {
            File dir = new File(path);
            if (dir.exists() && dir.isDirectory()) {
                File scriptFile = new File(dir, scriptName);
                if (scriptFile.exists()) {
                    logger.debug("在向后兼容路径中找到脚本文件: {}", scriptFile.getAbsolutePath());
                    return dir.getAbsolutePath();
                }
            }
        }

        return null;
    }

    /**
     * 获取所有可能的搜索路径（用于诊断）
     */
    public List<String> getAllSearchPaths() {
        List<String> allPaths = new ArrayList<>();
        
        // 配置的根目录
        String configuredRoot = config.getProjectRoot();
        if (!configuredRoot.isEmpty()) {
            allPaths.add("配置文件指定: " + configuredRoot);
        }
        
        // 项目标识文件路径
        try {
            URL classUrl = this.getClass().getProtectionDomain().getCodeSource().getLocation();
            Path currentPath = Paths.get(classUrl.toURI()).getParent();
            while (currentPath != null && allPaths.size() < 10) {
                allPaths.add("类路径向上: " + currentPath);
                currentPath = currentPath.getParent();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        // 备用路径
        List<String> fallbackPaths = config.getFallbackPaths();
        for (String path : fallbackPaths) {
            if (!path.trim().isEmpty()) {
                allPaths.add("备用路径: " + path.trim());
            }
        }
        
        return allPaths;
    }
}
