package com.building.service.impl;

import com.building.dao.CameraDao;
import com.building.model.Camera;
import com.building.service.VideoStreamService;
import com.building.config.StreamServerConfig;
import com.building.util.ProjectPathResolver;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 视频流服务实现类
 */
public class VideoStreamServiceImpl implements VideoStreamService {

    private static final Logger logger = LoggerFactory.getLogger(VideoStreamServiceImpl.class);

    private CameraDao cameraDao;
    private Gson gson;
    private StreamServerConfig config;
    private ProjectPathResolver pathResolver;

    // 流服务器进程管理
    private static Process streamServerProcess = null;

    // 用户手动停止标志
    private static boolean userManuallyStopped = false;

    /**
     * 构造函数
     */
    public VideoStreamServiceImpl() {
        cameraDao = new CameraDao();
        gson = new Gson();
        config = StreamServerConfig.getInstance();
        pathResolver = new ProjectPathResolver();

        logger.info("VideoStreamServiceImpl 初始化完成");
    }
    
    @Override
    public boolean startCameraStream(int cameraId) {
        System.out.println("开始启动摄像头流: " + cameraId);

        try {
            // 首先检查流服务器是否可用
            if (!checkStreamServerHealth()) {
                System.err.println("流服务器不可用，请确保Node.js流服务器已启动");
                return false;
            }

            Camera camera = cameraDao.getCameraById(cameraId);
            if (camera == null) {
                System.err.println("摄像头不存在: " + cameraId);
                return false;
            }

            if (camera.getRtspUrl() == null || camera.getRtspUrl().trim().isEmpty()) {
                System.err.println("摄像头RTSP URL为空: " + cameraId);
                return false;
            }

            // 生成流ID（如果不存在）
            String streamId = camera.getStreamId();
            if (streamId == null || streamId.trim().isEmpty()) {
                streamId = "camera_" + cameraId;
                camera.setStreamId(streamId);
            }

            System.out.println("准备启动流 - 摄像头ID: " + cameraId + ", 流ID: " + streamId + ", RTSP URL: " + camera.getRtspUrl());

            // 准备请求数据
            JsonObject requestData = new JsonObject();
            requestData.addProperty("rtspUrl", camera.getRtspUrl());
            requestData.addProperty("streamId", streamId);
            requestData.addProperty("format", camera.getStreamFormat() != null ? camera.getStreamFormat() : "hls");

            System.out.println("发送请求到流服务器: " + config.getServerUrl() + "/api/stream/start");
            System.out.println("请求数据: " + requestData.toString());

            // 发送启动流请求
            String response = sendHttpRequest("/api/stream/start", "POST", requestData.toString());
            System.out.println("收到响应: " + response);

            JsonObject responseObj = gson.fromJson(response, JsonObject.class);
            
            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                // 更新数据库中的流状态
                JsonObject data = responseObj.getAsJsonObject("data");
                String streamUrl = data.get("outputUrl").getAsString();

                // 先设置为启动中状态
                cameraDao.updateCameraStreamStatus(cameraId, 1, streamUrl);

                System.out.println("摄像头流启动请求成功: " + cameraId + ", 流URL: " + streamUrl);
                System.out.println("FFmpeg进程正在后台启动，请稍等片刻后检查流状态");

                // 启动后台任务检查流状态
                checkStreamStatusAsync(cameraId, streamUrl);

                return true;
            } else {
                String error = responseObj != null && responseObj.has("error") ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("启动摄像头流失败: " + cameraId + ", 错误: " + error);

                // 更新错误状态
                cameraDao.updateCameraStreamStatus(cameraId, 2, null);
                cameraDao.incrementStreamErrorCount(cameraId);

                return false;
            }
        } catch (Exception e) {
            System.err.println("启动摄像头流异常: " + cameraId + ", " + e.getMessage());
            e.printStackTrace();
            
            // 更新错误状态
            cameraDao.updateCameraStreamStatus(cameraId, 2, null);
            cameraDao.incrementStreamErrorCount(cameraId);
            
            return false;
        }
    }
    
    @Override
    public boolean stopCameraStream(int cameraId) {
        try {
            Camera camera = cameraDao.getCameraById(cameraId);
            if (camera == null) {
                System.err.println("摄像头不存在: " + cameraId);
                return false;
            }
            
            String streamId = camera.getStreamId();
            if (streamId == null || streamId.trim().isEmpty()) {
                System.out.println("摄像头流ID为空，无需停止: " + cameraId);
                return true;
            }
            
            // 准备请求数据
            JsonObject requestData = new JsonObject();
            requestData.addProperty("streamId", streamId);
            
            // 发送停止流请求
            String response = sendHttpRequest("/api/stream/stop", "POST", requestData.toString());
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);
            
            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                // 更新数据库中的流状态
                cameraDao.updateCameraStreamStatus(cameraId, 0, null);
                
                System.out.println("摄像头流停止成功: " + cameraId);
                return true;
            } else {
                String error = responseObj != null ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("停止摄像头流失败: " + cameraId + ", 错误: " + error);
                return false;
            }
        } catch (Exception e) {
            System.err.println("停止摄像头流异常: " + cameraId + ", " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean restartCameraStream(int cameraId) {
        System.out.println("重启摄像头流: " + cameraId);
        
        // 先停止，再启动
        stopCameraStream(cameraId);
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return startCameraStream(cameraId);
    }
    
    @Override
    public String getCameraStreamStatus(int cameraId) {
        try {
            Camera camera = cameraDao.getCameraById(cameraId);
            if (camera == null) {
                return "摄像头不存在";
            }
            
            switch (camera.getStreamStatus()) {
                case 0:
                    return "未启动";
                case 1:
                    return "运行中";
                case 2:
                    return "错误";
                default:
                    return "未知状态";
            }
        } catch (Exception e) {
            System.err.println("获取摄像头流状态异常: " + cameraId + ", " + e.getMessage());
            return "获取状态失败";
        }
    }

    /**
     * 异步检查流状态
     * @param cameraId 摄像头ID
     * @param streamUrl 流URL
     */
    private void checkStreamStatusAsync(int cameraId, String streamUrl) {
        // 使用新线程异步检查
        new Thread(() -> {
            try {
                // 等待更长时间让FFmpeg生成HLS文件
                System.out.println("等待FFmpeg生成HLS文件，摄像头ID: " + cameraId);

                // 多次检查，给FFmpeg足够时间
                boolean streamAvailable = false;
                for (int i = 0; i < 6; i++) { // 检查6次，每次间隔5秒，总共30秒
                    Thread.sleep(5000);
                    streamAvailable = checkStreamAvailability(streamUrl);

                    if (streamAvailable) {
                        System.out.println("摄像头流状态检查：流可用 - " + cameraId + " (第" + (i+1) + "次检查)");
                        break;
                    } else {
                        System.out.println("摄像头流状态检查：流暂不可用 - " + cameraId + " (第" + (i+1) + "次检查，继续等待)");
                    }
                }

                if (!streamAvailable) {
                    System.err.println("摄像头流状态检查：经过30秒检查，流仍不可用 - " + cameraId);
                    // 只有在多次检查后仍不可用才标记为错误
                    cameraDao.updateCameraStreamStatus(cameraId, 2, null);
                    cameraDao.incrementStreamErrorCount(cameraId);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("流状态检查被中断: " + cameraId);
            } catch (Exception e) {
                System.err.println("流状态检查异常: " + cameraId + ", " + e.getMessage());
                // 只有在异常情况下才标记为错误
                cameraDao.updateCameraStreamStatus(cameraId, 2, null);
                cameraDao.incrementStreamErrorCount(cameraId);
            }
        }).start();
    }

    /**
     * 检查流是否可用
     * @param streamUrl 流URL
     * @return 是否可用
     */
    private boolean checkStreamAvailability(String streamUrl) {
        try {
            if (streamUrl != null && streamUrl.contains("playlist.m3u8")) {
                // 对于HLS流，检查playlist文件是否存在且有内容
                String filePath = streamUrl.replace("http://localhost:3001/", "public/");
                java.io.File file = new java.io.File(filePath);

                if (!file.exists()) {
                    System.out.println("播放列表文件不存在: " + filePath);
                    return false;
                }

                // 检查文件是否有内容（至少包含一个片段）
                try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(file))) {
                    String line;
                    boolean hasSegment = false;
                    while ((line = reader.readLine()) != null) {
                        if (line.endsWith(".ts")) {
                            hasSegment = true;
                            break;
                        }
                    }

                    if (hasSegment) {
                        System.out.println("播放列表文件可用，包含视频片段: " + filePath);
                        return true;
                    } else {
                        System.out.println("播放列表文件存在但暂无视频片段: " + filePath);
                        return false;
                    }
                } catch (Exception e) {
                    System.err.println("读取播放列表文件失败: " + e.getMessage());
                    return false;
                }
            }
            return true; // 其他情况默认认为可用
        } catch (Exception e) {
            System.err.println("检查流可用性异常: " + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean checkStreamServerHealth() {
        logger.debug("检查流服务器健康状态: {}/api/health", config.getServerUrl());

        try {
            String response = sendHttpRequest("/api/health", "GET", null);
            logger.debug("健康检查响应: {}", response);

            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            boolean isHealthy = responseObj != null && responseObj.get("success").getAsBoolean();
            logger.debug("流服务器健康状态: {}", isHealthy ? "健康" : "不健康");

            return isHealthy;
        } catch (Exception e) {
            logger.debug("检查流服务器健康状态失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public String getCameraStreamUrl(Camera camera) {
        if (camera == null || camera.getStreamUrl() == null) {
            return null;
        }
        
        // 返回完整的流URL
        return config.getServerUrl() + camera.getStreamUrl();
    }
    
    /**
     * 发送HTTP请求
     * @param endpoint 端点
     * @param method 请求方法
     * @param requestBody 请求体
     * @return 响应内容
     * @throws IOException 如果发生IO异常
     */
    private String sendHttpRequest(String endpoint, String method, String requestBody) throws IOException {
        String fullUrl = config.getServerUrl() + endpoint;
        logger.debug("发送HTTP请求: {} {}", method, fullUrl);

        URL url = new URL(fullUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod(method);
        connection.setConnectTimeout(config.getConnectionTimeout());
        connection.setReadTimeout(config.getReadTimeout());
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");

        logger.debug("连接超时: {}ms, 读取超时: {}ms", config.getConnectionTimeout(), config.getReadTimeout());
        
        if (requestBody != null && ("POST".equals(method) || "PUT".equals(method))) {
            logger.debug("发送请求体: {}", requestBody);
            connection.setDoOutput(true);
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
        }

        // 尝试连接并获取响应码
        int responseCode;
        try {
            responseCode = connection.getResponseCode();
            logger.debug("HTTP响应码: {}", responseCode);
        } catch (IOException e) {
            logger.error("获取响应码失败: {}", e.getMessage());
            throw e;
        }

        // 读取响应
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(
                responseCode >= 200 && responseCode < 300
                    ? connection.getInputStream()
                    : connection.getErrorStream(),
                StandardCharsets.UTF_8))) {

            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        String responseStr = response.toString();
        logger.debug("HTTP响应内容: {}", responseStr);

        return responseStr;
    }

    /**
     * 获取项目根目录
     * @return 项目根目录路径
     */
    private String getProjectRootDirectory() {
        logger.debug("开始查找项目根目录...");

        try {
            String projectRoot = pathResolver.findProjectRoot();
            if (projectRoot != null) {
                logger.info("找到项目根目录: {}", projectRoot);
                return projectRoot;
            }
        } catch (Exception e) {
            logger.error("查找项目根目录时发生异常: {}", e.getMessage(), e);
        }

        // 如果新的路径解析器失败，回退到当前工作目录
        String fallbackPath = System.getProperty("user.dir");
        logger.warn("使用回退路径: {}", fallbackPath);
        return fallbackPath;
    }

    /**
     * 查找脚本文件
     * @param projectRoot 项目根目录
     * @return 脚本文件路径，如果找不到则返回null
     */
    private String findScriptFile(String projectRoot) {
        logger.debug("在项目根目录 {} 中查找脚本文件", projectRoot);

        try {
            String scriptPath = pathResolver.findScriptFile(projectRoot);
            if (scriptPath != null) {
                logger.info("找到脚本文件: {}", scriptPath);
                return scriptPath;
            }
        } catch (Exception e) {
            logger.error("查找脚本文件时发生异常: {}", e.getMessage(), e);
        }

        logger.error("未找到脚本文件");
        return null;
    }

    /**
     * 记录诊断信息，帮助排查路径问题
     */
    private void logDiagnosticInfo() {
        logger.error("=== 流服务器脚本查找诊断信息 ===");
        logger.error("脚本名称: {}", config.getScriptName());
        logger.error("配置的项目根目录: {}", config.getProjectRoot());

        logger.error("所有搜索路径:");
        List<String> allPaths = pathResolver.getAllSearchPaths();
        for (int i = 0; i < allPaths.size(); i++) {
            logger.error("  {}. {}", i + 1, allPaths.get(i));
        }

        logger.error("脚本搜索路径: {}", config.getScriptSearchPaths());
        logger.error("项目标识文件: {}", config.getProjectMarkers());
        logger.error("备用路径: {}", config.getFallbackPaths());

        logger.error("当前工作目录: {}", System.getProperty("user.dir"));
        logger.error("Java类路径: {}", System.getProperty("java.class.path"));

        // 检查当前工作目录下是否有脚本文件
        File currentDirScript = new File(System.getProperty("user.dir"), config.getScriptName());
        logger.error("当前目录下脚本文件存在: {}", currentDirScript.exists());

        logger.error("=== 诊断信息结束 ===");
    }

    @Override
    public boolean startStreamServer() {
        logger.info("尝试启动流服务器...");

        // 检查是否已经运行
        if (config.isHealthCheckEnabled()) {
            try {
                if (checkStreamServerHealth()) {
                    logger.info("流服务器已经在运行中");
                    userManuallyStopped = false;
                    return true;
                }
            } catch (Exception e) {
                logger.warn("健康检查失败，继续尝试启动: {}", e.getMessage());
            }
        }

        try {
            // 获取项目根目录
            String projectRoot = getProjectRootDirectory();
            if (projectRoot == null) {
                logger.error("无法确定项目根目录");
                return false;
            }

            // 查找脚本文件
            String scriptPath = findScriptFile(projectRoot);
            if (scriptPath == null) {
                logger.error("无法找到流服务器脚本文件");
                // 提供诊断信息
                logDiagnosticInfo();
                return false;
            }

            File scriptFile = new File(scriptPath);
            logger.info("启动流服务器脚本: {}", scriptFile.getAbsolutePath());

            // 构建命令
            String nodeCommand = config.getNodeCommand();
            String scriptName = config.getScriptName();
            ProcessBuilder processBuilder = new ProcessBuilder(nodeCommand, scriptName);
            processBuilder.directory(new File(projectRoot));
            processBuilder.redirectErrorStream(true);

            logger.info("执行命令: {} {}", nodeCommand, scriptName);
            logger.info("工作目录: {}", projectRoot);

            // 启动进程
            streamServerProcess = processBuilder.start();

            // 启动一个线程来读取进程输出
            Thread outputReader = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(streamServerProcess.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (config.isDetailedLogging()) {
                            logger.info("[流服务器] {}", line);
                        }
                    }
                } catch (IOException e) {
                    logger.error("读取流服务器输出失败: {}", e.getMessage());
                }
            });
            outputReader.setDaemon(true);
            outputReader.start();

            // 等待一段时间让服务器启动
            logger.info("等待流服务器启动...");
            Thread.sleep(5000);

            // 检查进程是否还在运行
            if (streamServerProcess.isAlive()) {
                logger.info("流服务器进程启动成功");

                // 验证服务器是否响应
                boolean isHealthy = false;
                if (config.isHealthCheckEnabled()) {
                    logger.info("开始健康检查...");
                    int maxRetries = config.getHealthCheckMaxRetries();
                    int retryInterval = config.getHealthCheckRetryInterval();

                    for (int i = 0; i < maxRetries; i++) {
                        try {
                            if (checkStreamServerHealth()) {
                                isHealthy = true;
                                logger.info("健康检查在第 {} 次尝试时成功", i + 1);
                                break;
                            }
                        } catch (Exception e) {
                            logger.debug("健康检查第 {} 次尝试失败: {}", i + 1, e.getMessage());
                        }
                        Thread.sleep(retryInterval);
                    }
                } else {
                    // 如果禁用了健康检查，直接认为启动成功
                    isHealthy = true;
                    logger.info("健康检查已禁用，跳过检查");
                }

                if (isHealthy) {
                    logger.info("流服务器启动成功");
                    userManuallyStopped = false;
                    return true;
                } else {
                    logger.warn("流服务器启动但健康检查失败，但进程仍在运行，认为启动成功");
                    userManuallyStopped = false;
                    return true;
                }
            } else {
                logger.error("流服务器进程启动失败");
                return false;
            }

        } catch (Exception e) {
            logger.error("启动流服务器异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean stopStreamServer() {
        System.out.println("尝试停止流服务器...");

        // 设置用户手动停止标志
        userManuallyStopped = true;

        if (streamServerProcess == null) {
            System.out.println("流服务器进程不存在");
            return true;
        }

        try {
            if (streamServerProcess.isAlive()) {
                System.out.println("正在停止流服务器进程...");
                streamServerProcess.destroy();

                // 等待进程结束
                boolean terminated = streamServerProcess.waitFor(10, TimeUnit.SECONDS);

                if (!terminated) {
                    System.out.println("强制终止流服务器进程...");
                    streamServerProcess.destroyForcibly();
                    streamServerProcess.waitFor(5, TimeUnit.SECONDS);
                }

                System.out.println("流服务器进程已停止");
            }

            streamServerProcess = null;
            return true;

        } catch (Exception e) {
            System.err.println("停止流服务器异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查用户是否手动停止了流服务器
     * @return 是否手动停止
     */
    public boolean isUserManuallyStopped() {
        return userManuallyStopped;
    }

    @Override
    public boolean isStreamServerRunning() {
        // 首先检查进程是否存在且活跃
        if (streamServerProcess != null && streamServerProcess.isAlive()) {
            // 进一步检查服务器是否响应
            return checkStreamServerHealth();
        }

        // 如果进程不存在，尝试通过健康检查确认
        return checkStreamServerHealth();
    }

    @Override
    public String getStreamServerHealthDetails() {
        try {
            String response = sendHttpRequest("/api/health", "GET", null);
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                StringBuilder details = new StringBuilder();
                details.append("服务器状态: 健康\n");
                details.append("消息: ").append(responseObj.get("message").getAsString()).append("\n");

                if (responseObj.has("ffmpeg")) {
                    details.append("FFmpeg状态: ").append(responseObj.get("ffmpeg").getAsString()).append("\n");
                }

                // 获取流状态信息
                try {
                    String statusResponse = sendHttpRequest("/api/stream/status", "GET", null);
                    JsonObject statusObj = gson.fromJson(statusResponse, JsonObject.class);

                    if (statusObj != null && statusObj.get("success").getAsBoolean()) {
                        JsonObject data = statusObj.getAsJsonObject("data");
                        details.append("活跃流数量: ").append(data.get("activeStreams").getAsInt()).append("\n");
                    }
                } catch (Exception e) {
                    details.append("获取流状态失败: ").append(e.getMessage()).append("\n");
                }

                return details.toString();
            } else {
                return "服务器状态: 不健康\n错误: " + (responseObj != null ? responseObj.get("error").getAsString() : "未知错误");
            }

        } catch (Exception e) {
            return "无法连接到流服务器: " + e.getMessage();
        }
    }

    @Override
    public boolean clearAllVideoCache() {
        System.out.println("开始清除所有视频缓存...");

        try {
            // 首先检查流服务器是否可用
            if (!checkStreamServerHealth()) {
                System.out.println("流服务器不可用，无法清除缓存");
                return false;
            }

            // 发送清除缓存请求到Node.js服务器
            String response = sendHttpRequest("/api/cache/clear", "POST", null);
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                System.out.println("视频缓存清除成功");
                return true;
            } else {
                String error = responseObj != null ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("清除视频缓存失败: " + error);
                return false;
            }

        } catch (Exception e) {
            System.err.println("清除视频缓存异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean stopAllActiveStreams() {
        System.out.println("开始停止所有活跃的视频流...");

        try {
            // 首先检查流服务器是否可用
            if (!checkStreamServerHealth()) {
                System.out.println("流服务器不可用，无法停止流");
                return false;
            }

            // 发送停止所有流的请求到Node.js服务器
            String response = sendHttpRequest("/api/stream/stop-all", "POST", null);
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                // 更新数据库中所有摄像头的流状态
                List<Camera> cameras = cameraDao.getAllCameras();
                if (cameras != null) {
                    for (Camera camera : cameras) {
                        if (camera.getStreamStatus() == 1) { // 只更新正在运行的流
                            cameraDao.updateCameraStreamStatus(camera.getId(), 0, null);
                        }
                    }
                }

                System.out.println("所有活跃视频流停止成功");
                return true;
            } else {
                String error = responseObj != null ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("停止所有活跃视频流失败: " + error);
                return false;
            }

        } catch (Exception e) {
            System.err.println("停止所有活跃视频流异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
