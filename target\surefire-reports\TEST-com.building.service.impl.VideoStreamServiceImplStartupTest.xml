<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.building.service.impl.VideoStreamServiceImplStartupTest" time="0.174" tests="3" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\eclipse-workspace1\jspPj002\target\test-classes;C:\Users\<USER>\eclipse-workspace1\jspPj002\target\classes;C:\Users\<USER>\.m2\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\javax\servlet\jsp\javax.servlet.jsp-api\2.3.3\javax.servlet.jsp-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\javax\servlet\jstl\1.2\jstl-1.2.jar;C:\Users\<USER>\.m2\repository\taglibs\standard\1.1.2\standard-1.1.2.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.11.4\protobuf-java-3.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.10\spring-context-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.10\spring-aop-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.10\spring-expression-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.10\spring-webmvc-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.10\spring-web-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.10\spring-beans-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.10\spring-core-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.10\spring-jcl-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.32\slf4j-api-1.7.32.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.6\logback-classic-1.2.6.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.6\logback-core-1.2.6.jar;C:\Users\<USER>\.m2\repository\javax\mail\javax.mail-api\1.6.2\javax.mail-api-1.6.2.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\javax.mail\1.6.2\javax.mail-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1\activation-1.1.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17.0.2\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire13320618069093787908\surefirebooter-20250624104659960_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire13320618069093787908 2025-06-24T10-46-59_839-jvmRun1 surefire-20250624104659960_1tmp surefire_0-20250624104659960_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="VideoStreamServiceImplStartupTest"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\eclipse-workspace1\jspPj002\target\test-classes;C:\Users\<USER>\eclipse-workspace1\jspPj002\target\classes;C:\Users\<USER>\.m2\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\javax\servlet\jsp\javax.servlet.jsp-api\2.3.3\javax.servlet.jsp-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\javax\servlet\jstl\1.2\jstl-1.2.jar;C:\Users\<USER>\.m2\repository\taglibs\standard\1.1.2\standard-1.1.2.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.11.4\protobuf-java-3.11.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.10\spring-context-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.10\spring-aop-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.10\spring-expression-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.10\spring-webmvc-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.10\spring-web-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.10\spring-beans-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.10\spring-core-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.10\spring-jcl-5.3.10.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.32\slf4j-api-1.7.32.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.6\logback-classic-1.2.6.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.6\logback-core-1.2.6.jar;C:\Users\<USER>\.m2\repository\javax\mail\javax.mail-api\1.6.2\javax.mail-api-1.6.2.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\javax.mail\1.6.2\javax.mail-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1\activation-1.1.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17.0.2"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\eclipse-workspace1\jspPj002"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire13320618069093787908\surefirebooter-20250624104659960_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.2+8-LTS-86"/>
    <property name="user.name" value="yy"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.2"/>
    <property name="user.dir" value="C:\Users\<USER>\eclipse-workspace1\jspPj002"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17.0.2\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\Anaconda3\condabin;C:\Program Files\PowerShell\7;C:\Program Files\MySQL\MySQL Server 8.3\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet\;C:\Program Files\nodejs\;C:\Program Files\Java\jdk-17.0.2\bin\;D:\Anaconda3\envs\2316406040\;C:\Program Files\PowerShell\7\;D:\Maven\apache-maven-3.9.9\bin;C:\Program Files\chrome-win64\;C:\Program Files\ffmpeg\bin;D:\MATLAB\R2024a\runtime\win64;D:\MATLAB\R2024a\bin;D:\Topaz\bin\;D:\ollama\;D:\Program Files\Git\cmd;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\ollama;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.2+8-LTS-86"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testServiceCanBeInstantiated" classname="com.building.service.impl.VideoStreamServiceImplStartupTest" time="0.138">
    <system-out><![CDATA[10:47:00.365 [main] DEBUG c.building.config.StreamServerConfig - 成功加载配置文件: stream-server.properties
10:47:00.368 [main] DEBUG c.building.config.StreamServerConfig - 配置文件不存在: stream-server-development.properties
10:47:00.369 [main] INFO  c.building.config.StreamServerConfig - 流服务器配置加载完成，环境: development
10:47:00.370 [main] INFO  c.b.s.impl.VideoStreamServiceImpl - VideoStreamServiceImpl 初始化完成
VideoStreamServiceImpl 创建成功
]]></system-out>
  </testcase>
  <testcase name="testConfigurationLoading" classname="com.building.service.impl.VideoStreamServiceImplStartupTest" time="0.002">
    <system-out><![CDATA[10:47:00.370 [main] INFO  c.b.s.impl.VideoStreamServiceImpl - VideoStreamServiceImpl 初始化完成
10:47:00.371 [main] DEBUG c.b.s.impl.VideoStreamServiceImpl - 开始查找项目根目录...
10:47:00.371 [main] DEBUG c.building.util.ProjectPathResolver - 开始查找项目根目录...
10:47:00.372 [main] DEBUG c.building.util.ProjectPathResolver - 找到项目标识文件: C:\Users\<USER>\eclipse-workspace1\jspPj002\package.json
10:47:00.372 [main] INFO  c.building.util.ProjectPathResolver - 通过项目标识文件找到项目根目录: C:\Users\<USER>\eclipse-workspace1\jspPj002
10:47:00.372 [main] INFO  c.b.s.impl.VideoStreamServiceImpl - 找到项目根目录: C:\Users\<USER>\eclipse-workspace1\jspPj002
项目根目录: C:\Users\<USER>\eclipse-workspace1\jspPj002
]]></system-out>
  </testcase>
  <testcase name="testLoggerInitialization" classname="com.building.service.impl.VideoStreamServiceImplStartupTest" time="0.001">
    <system-out><![CDATA[10:47:00.373 [main] INFO  c.b.s.impl.VideoStreamServiceImpl - VideoStreamServiceImpl 初始化完成
日志系统初始化成功
]]></system-out>
  </testcase>
</testsuite>