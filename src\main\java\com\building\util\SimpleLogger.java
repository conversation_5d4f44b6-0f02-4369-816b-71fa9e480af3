package com.building.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 简单的日志工具类，用于替代SLF4J以避免依赖问题
 */
public class SimpleLogger {
    
    private final String className;
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public SimpleLogger(Class<?> clazz) {
        this.className = clazz.getSimpleName();
    }
    
    public static SimpleLogger getLogger(Class<?> clazz) {
        return new SimpleLogger(clazz);
    }
    
    public void info(String message) {
        log("INFO", message);
    }
    
    public void info(String format, Object... args) {
        log("INFO", formatMessage(format, args));
    }
    
    public void debug(String message) {
        log("DEBUG", message);
    }
    
    public void debug(String format, Object... args) {
        log("DEBUG", formatMessage(format, args));
    }
    
    public void warn(String message) {
        log("WARN", message);
    }
    
    public void warn(String format, Object... args) {
        log("WARN", formatMessage(format, args));
    }
    
    public void error(String message) {
        log("ERROR", message);
    }
    
    public void error(String format, Object... args) {
        log("ERROR", formatMessage(format, args));
    }
    
    public void error(String format, Object arg1, Throwable throwable) {
        log("ERROR", formatMessage(format, arg1));
        if (throwable != null) {
            throwable.printStackTrace();
        }
    }
    
    private void log(String level, String message) {
        String timestamp = DATE_FORMAT.format(new Date());
        System.out.println(String.format("[%s] %s [%s] %s", timestamp, level, className, message));
    }
    
    private String formatMessage(String format, Object... args) {
        if (args == null || args.length == 0) {
            return format;
        }
        
        String result = format;
        for (Object arg : args) {
            int index = result.indexOf("{}");
            if (index >= 0) {
                String argStr = arg != null ? arg.toString() : "null";
                result = result.substring(0, index) + argStr + result.substring(index + 2);
            }
        }
        return result;
    }
}
