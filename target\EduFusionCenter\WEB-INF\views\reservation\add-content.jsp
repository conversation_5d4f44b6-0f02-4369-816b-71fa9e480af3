<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid px-4 py-3">
    <!-- 页面标题区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header-card bg-white rounded-4 shadow-sm p-4 mb-4">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="page-title-section mb-3 mb-lg-0">
                        <div class="d-flex align-items-center mb-2">
                            <div class="page-icon-wrapper me-3">
                                <i class="bi bi-plus-circle text-primary"></i>
                            </div>
                            <div>
                                <h1 class="page-title mb-1">添加预约</h1>
                                <p class="page-subtitle text-muted mb-0">创建新的房间或设备预约申请</p>
                            </div>
                        </div>
                    </div>
                    <div class="page-actions">
                        <a href="${pageContext.request.contextPath}/reservation/list" class="btn btn-outline-secondary rounded-pill px-4 d-flex align-items-center">
                            <i class="bi bi-arrow-left me-2"></i> 返回列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表单区域 -->
    <div class="form-section">
        <div class="card border-0 shadow-sm rounded-4">
            <div class="card-header bg-gradient border-0 py-4">
                <h5 class="card-title mb-0 fw-bold text-dark">
                    <i class="bi bi-form-check me-2 text-primary"></i>预约信息
                </h5>
            </div>
            <div class="card-body p-4">
                <form id="addReservationForm" action="${pageContext.request.contextPath}/reservation/add" method="post">
                    <!-- 预约类型选择 -->
                    <div class="form-group-section mb-5">
                        <div class="form-group-header mb-4">
                            <h6 class="form-section-title text-muted fw-bold mb-3">
                                <i class="bi bi-1-circle me-2"></i>选择预约类型
                            </h6>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="reservation-type-selector">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="type-option">
                                                <input class="form-check-input" type="radio" name="reservationType" id="roomType" value="room" checked>
                                                <label class="type-option-label" for="roomType">
                                                    <div class="type-icon">
                                                        <i class="bi bi-building"></i>
                                                    </div>
                                                    <div class="type-content">
                                                        <h6 class="type-title">预约房间</h6>
                                                        <p class="type-description">预约会议室、办公室等房间资源</p>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="type-option">
                                                <input class="form-check-input" type="radio" name="reservationType" id="deviceType" value="device">
                                                <label class="type-option-label" for="deviceType">
                                                    <div class="type-icon">
                                                        <i class="bi bi-laptop"></i>
                                                    </div>
                                                    <div class="type-content">
                                                        <h6 class="type-title">预约设备</h6>
                                                        <p class="type-description">预约投影仪、电脑等设备资源</p>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 资源选择 -->
                    <div class="form-group-section mb-5">
                        <div class="form-group-header mb-4">
                            <h6 class="form-section-title text-muted fw-bold mb-3">
                                <i class="bi bi-2-circle me-2"></i>选择预约资源
                            </h6>
                        </div>
                        <div class="row">
                            <div class="col-12" id="roomSelection">
                                <label class="form-label fw-semibold">选择房间</label>
                                <select class="form-select form-select-lg" name="roomId">
                                    <option value="">请选择要预约的房间</option>
                                    <c:forEach items="${rooms}" var="room">
                                        <option value="${room.id}">${room.roomNumber} - ${room.roomType}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="col-12" id="deviceSelection" style="display: none;">
                                <label class="form-label fw-semibold">选择设备</label>
                                <select class="form-select form-select-lg" name="deviceId">
                                    <option value="">请选择要预约的设备</option>
                                    <c:forEach items="${devices}" var="device">
                                        <option value="${device.id}">${device.name} - ${device.type} (${device.location})</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                    </div>
                
                    <!-- 预约详情 -->
                    <div class="form-group-section mb-5">
                        <div class="form-group-header mb-4">
                            <h6 class="form-section-title text-muted fw-bold mb-3">
                                <i class="bi bi-3-circle me-2"></i>填写预约详情
                            </h6>
                        </div>
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">使用目的 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control form-control-lg" name="purpose" placeholder="请输入使用目的" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">备注说明</label>
                                <input type="text" class="form-control form-control-lg" name="description" placeholder="请输入备注信息（可选）">
                            </div>
                        </div>
                    </div>

                    <!-- 时间设置 -->
                    <div class="form-group-section mb-5">
                        <div class="form-group-header mb-4">
                            <h6 class="form-section-title text-muted fw-bold mb-3">
                                <i class="bi bi-4-circle me-2"></i>设置预约时间
                            </h6>
                        </div>
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">开始时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control form-control-lg" name="startTime" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">结束时间 <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control form-control-lg" name="endTime" required>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="form-actions pt-4 border-top">
                        <div class="d-flex justify-content-end gap-3">
                            <button type="button" class="btn btn-outline-secondary btn-lg rounded-pill px-5" onclick="window.location.href='${pageContext.request.contextPath}/reservation/list'">
                                <i class="bi bi-x-lg me-2"></i>取消
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg rounded-pill px-5">
                                <i class="bi bi-check-lg me-2"></i>提交预约
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addReservationForm');
    const roomSelection = document.getElementById('roomSelection');
    const deviceSelection = document.getElementById('deviceSelection');
    const roomSelect = roomSelection.querySelector('select');
    const deviceSelect = deviceSelection.querySelector('select');
    
    // 监听预约类型切换
    document.querySelectorAll('input[name="reservationType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'room') {
                roomSelection.style.display = 'block';
                deviceSelection.style.display = 'none';
                roomSelect.setAttribute('required', '');
                deviceSelect.removeAttribute('required');
                deviceSelect.value = '';
            } else {
                roomSelection.style.display = 'none';
                deviceSelection.style.display = 'block';
                deviceSelect.setAttribute('required', '');
                roomSelect.removeAttribute('required');
                roomSelect.value = '';
            }
        });
    });
    
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // 收集表单数据
        const formData = new URLSearchParams();
        const formElements = form.elements;
        
        // 根据预约类型设置ID
        const reservationType = form.querySelector('input[name="reservationType"]:checked').value;
        if (reservationType === 'room') {
            formData.append('roomId', roomSelect.value);
            formData.append('deviceId', '');
        } else {
            formData.append('deviceId', deviceSelect.value);
            formData.append('roomId', '');
        }
        
        // 添加其他表单数据
        for (let i = 0; i < formElements.length; i++) {
            const element = formElements[i];
            if (element.name && element.value && 
                element.name !== 'roomId' && 
                element.name !== 'deviceId' &&
                element.name !== 'reservationType') {
                formData.append(element.name, element.value);
            }
        }
        
        fetch('${pageContext.request.contextPath}/reservation/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData.toString()
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('预约添加成功');
                window.location.href = '${pageContext.request.contextPath}/reservation/list';
            } else {
                alert('预约添加失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('系统错误，请稍后重试');
        });
    });
});
</script> 