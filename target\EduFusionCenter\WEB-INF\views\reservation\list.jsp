<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="预约管理" />
    <jsp:param name="content" value="/WEB-INF/views/reservation/content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 页面整体样式 */
        body {
            background-color: #f8f9fa;
        }

        /* 页面标题区域样式 */
        .page-header-card {
            border: 1px solid rgba(0,0,0,0.05);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }
        .page-icon-wrapper {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .page-icon-wrapper i {
            font-size: 1.8rem;
            color: white;
        }
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }
        .page-subtitle {
            font-size: 1rem;
            line-height: 1.5;
        }
        .btn-add-reservation {
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            transition: all 0.3s ease;
        }
        .btn-add-reservation:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,123,255,0.4);
        }

        /* 筛选工具栏样式 */
        .filter-section {
            margin-bottom: 2rem;
        }
        .filter-container {
            border: 1px solid rgba(0,0,0,0.05);
        }
        .filter-section-title {
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }
        .filter-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        .filter-input {
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .filter-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        /* 统计卡片样式 */
        .stats-section {
            margin-bottom: 2rem;
        }
        .stats-card {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            position: relative;
            border: none;
            transition: all 0.4s ease;
            min-height: 160px;
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stats-content {
            flex: 1;
        }
        .stats-icon-container {
            width: 50px;
            height: 50px;
            background-color: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .stats-card:hover .stats-icon-container {
            transform: scale(1.1);
            background-color: rgba(255,255,255,0.3);
        }
        .stats-icon {
            font-size: 1.5rem;
            color: white;
        }
        .stats-number {
            font-size: 2.2rem;
            font-weight: 800;
            color: white;
            line-height: 1;
        }
        .stats-label {
            font-size: 0.95rem;
            color: rgba(255,255,255,0.9);
            font-weight: 500;
        }
        .stats-trend {
            text-align: center;
            opacity: 0.8;
        }
        .stats-trend i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
            display: block;
        }
        .trend-text {
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* 预约卡片样式 */
        .reservations-section {
            margin-bottom: 3rem;
        }
        .reservation-card {
            border-radius: 16px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .reservation-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.15) !important;
        }
    " />
</jsp:include>