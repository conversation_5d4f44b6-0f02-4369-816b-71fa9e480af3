# 部署验证指南

## 问题解决方案

### 最终解决方案
经过多次尝试，最终采用的解决方案是：
1. **将SLF4J依赖包含在WAR文件中**：使用默认的compile scope
2. **确保所有日志依赖完整**：包含slf4j-api和logback-classic
3. **统一日志框架使用**：所有类都使用SLF4J，保持一致性

### 修复的关键点
```xml
<!-- 在pom.xml中确保SLF4J依赖被包含 -->
<dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-api</artifactId>
    <version>1.7.32</version>
</dependency>
<dependency>
    <groupId>ch.qos.logback</groupId>
    <artifactId>logback-classic</artifactId>
    <version>1.2.6</version>
</dependency>
```

## 验证步骤

### 1. 编译验证
```bash
mvn clean compile
# 应该显示: BUILD SUCCESS
```

### 2. 测试验证
```bash
mvn test -Dtest=VideoStreamServiceImplStartupTest
# 应该显示: Tests run: 3, Failures: 0, Errors: 0, Skipped: 0
```

### 3. 打包验证
```bash
mvn package -DskipTests
# 应该生成: target/EduFusionCenter.war
```

### 4. 依赖验证
```bash
# 检查WAR文件中的SLF4J依赖
jar -tf target/EduFusionCenter.war | findstr slf4j
# 应该显示: WEB-INF/lib/slf4j-api-1.7.32.jar

# 检查Logback依赖
jar -tf target/EduFusionCenter.war | findstr logback
# 应该显示: 
# WEB-INF/classes/logback.xml
# WEB-INF/lib/logback-classic-1.2.6.jar
# WEB-INF/lib/logback-core-1.2.6.jar
```

## 部署到Tomcat

### 1. 停止Tomcat服务器
```bash
# Windows
catalina.bat stop

# Linux
./catalina.sh stop
```

### 2. 清理旧部署
```bash
# 删除旧的WAR文件和解压目录
rm -rf $CATALINA_HOME/webapps/EduFusionCenter*
```

### 3. 部署新WAR文件
```bash
# 复制新的WAR文件到webapps目录
cp target/EduFusionCenter.war $CATALINA_HOME/webapps/
```

### 4. 启动Tomcat服务器
```bash
# Windows
catalina.bat start

# Linux
./catalina.sh start
```

### 5. 验证部署
```bash
# 检查Tomcat日志
tail -f $CATALINA_HOME/logs/catalina.out

# 应该看到类似以下的成功信息：
# INFO: Deployment of web application archive [EduFusionCenter.war] has finished
```

## 功能验证

### 1. 访问应用主页
```
http://localhost:8080/EduFusionCenter/
```

### 2. 测试流服务器配置
访问摄像头管理页面，测试流服务器自动启动功能。

### 3. 检查日志输出
在Tomcat日志中应该能看到：
```
INFO  c.building.config.StreamServerConfig - 流服务器配置加载完成，环境: development
INFO  c.b.s.impl.VideoStreamServiceImpl - VideoStreamServiceImpl 初始化完成
DEBUG c.building.util.ProjectPathResolver - 通过项目标识文件找到项目根目录: /path/to/project
```

## 配置优化（可选）

### 1. 生产环境配置
创建`stream-server-prod.properties`：
```properties
stream.server.environment=production
stream.server.log.level=INFO
stream.server.log.detailed=false
stream.server.project.root=/opt/edufusioncenter
```

### 2. 设置环境变量
```bash
export STREAM_SERVER_PROFILE=prod
export STREAM_SERVER_PROJECT_ROOT=/opt/edufusioncenter
```

### 3. 系统属性配置
在Tomcat启动脚本中添加：
```bash
-Dstream.server.profile=prod
-Dstream.server.project.root=/opt/edufusioncenter
```

## 故障排除

### 如果仍然遇到ClassNotFoundException
1. 检查WAR文件中是否包含SLF4J JAR文件
2. 确认Tomcat版本兼容性
3. 检查是否有其他应用冲突

### 如果配置不生效
1. 检查配置文件是否在classpath中
2. 验证环境变量和系统属性设置
3. 查看应用启动日志

### 如果路径查找失败
1. 检查项目标识文件（package.json, pom.xml）是否存在
2. 验证stream-server.js文件位置
3. 查看详细的诊断日志

## 成功指标

部署成功的标志：
1. ✅ Tomcat启动无错误
2. ✅ 应用正常加载
3. ✅ 配置系统正常工作
4. ✅ 路径解析功能正常
5. ✅ 日志输出正常
6. ✅ 流服务器功能可用

## 联系支持

如果遇到问题，请提供：
1. Tomcat版本信息
2. 完整的错误日志
3. 应用配置信息
4. 环境变量设置
