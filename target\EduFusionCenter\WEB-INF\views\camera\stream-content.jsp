<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid py-4">
    <!-- 返回按钮 -->
    <div class="mb-4">
        <a href="${pageContext.request.contextPath}/camera/detail?id=${camera.id}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> 返回摄像头详情
        </a>
    </div>
    
    <!-- 视频流容器 -->
    <div class="stream-container">
        <!-- 视频流信息 -->
        <div class="stream-info">
            <h5 class="mb-1">
                <span class="camera-status ${camera.status == 1 ? 'camera-online' : 'camera-offline'}"></span>
                ${camera.name}
            </h5>
            <p class="mb-0 text-white-50">
                <i class="bi bi-geo-alt me-1"></i> ${camera.location}
                <c:if test="${camera.room != null}">
                    <span class="mx-2">|</span>
                    <i class="bi bi-building me-1"></i> ${camera.room.roomNumber}
                </c:if>
            </p>
        </div>
        
        <!-- 视频流内容 -->
        <c:choose>
            <c:when test="${camera.status == 1}">
                <!-- HLS视频播放器 -->
                <video id="videoPlayer" controls autoplay muted style="width: 100%; height: 100%; object-fit: contain; display: none;">
                    您的浏览器不支持视频播放
                </video>
                <!-- 流状态显示 -->
                <div id="streamStatus" class="stream-overlay" style="display: flex;">
                    <i class="bi bi-play-circle mb-3 fs-1"></i>
                    <h4>准备播放视频流</h4>
                    <p id="statusMessage">正在检查流状态...</p>
                    <div class="mt-3">
                        <button id="startStreamBtn" class="btn btn-success me-2">
                            <i class="bi bi-play-fill me-1"></i> 启动视频流
                        </button>
                        <button id="refreshStreamBtn" class="btn btn-outline-light">
                            <i class="bi bi-arrow-clockwise me-1"></i> 刷新状态
                        </button>
                    </div>
                </div>
            </c:when>
            <c:otherwise>
                <div class="stream-overlay">
                    <i class="bi bi-camera-video-off mb-3 fs-1"></i>
                    <h4>摄像头离线</h4>
                    <p>请连接摄像头后查看视频流</p>
                    <button id="connectBtn" class="btn btn-primary mt-3" data-action="connect">
                        <i class="bi bi-wifi me-1"></i> 连接摄像头
                    </button>
                </div>
            </c:otherwise>
        </c:choose>
        
        <!-- 视频流控制 -->
        <c:if test="${camera.status == 1}">
            <div class="stream-controls">
                <!-- 流控制按钮 -->
                <button id="stopStreamBtn" class="btn btn-warning" title="停止视频流" style="display: none;">
                    <i class="bi bi-stop-fill"></i>
                </button>
                <button id="restartStreamBtn" class="btn btn-info" title="重启视频流" style="display: none;">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>

                <!-- 摄像头控制按钮 -->
                <button class="btn control-btn" data-action="pan_left" title="向左">
                    <i class="bi bi-arrow-left"></i>
                </button>
                <button class="btn control-btn" data-action="tilt_up" title="向上">
                    <i class="bi bi-arrow-up"></i>
                </button>
                <button class="btn control-btn" data-action="home" title="回中心">
                    <i class="bi bi-house"></i>
                </button>
                <button class="btn control-btn" data-action="tilt_down" title="向下">
                    <i class="bi bi-arrow-down"></i>
                </button>
                <button class="btn control-btn" data-action="pan_right" title="向右">
                    <i class="bi bi-arrow-right"></i>
                </button>
                <button class="btn control-btn" data-action="zoom_out" title="缩小">
                    <i class="bi bi-dash-lg"></i>
                </button>
                <button class="btn control-btn" data-action="zoom_in" title="放大">
                    <i class="bi bi-plus-lg"></i>
                </button>
                <button class="btn btn-primary" id="fullscreenBtn" title="全屏">
                    <i class="bi bi-fullscreen"></i>
                </button>
                <button id="disconnectBtn" class="btn btn-danger" data-action="disconnect" title="断开摄像头">
                    <i class="bi bi-wifi-off"></i>
                </button>
            </div>
        </c:if>
    </div>
</div>
