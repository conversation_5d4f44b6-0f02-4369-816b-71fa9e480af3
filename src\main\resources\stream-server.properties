# 流服务器配置文件
# Stream Server Configuration

# 服务器基本配置
stream.server.url=http://127.0.0.1:3001
stream.server.connection.timeout=15000
stream.server.read.timeout=30000

# Node.js 配置
stream.server.node.command=node
stream.server.script.name=stream-server.js

# 项目路径配置
# 项目根目录路径（可选，留空则自动检测）
stream.server.project.root=

# 脚本查找路径（相对于项目根目录，多个路径用逗号分隔）
stream.server.script.search.paths=.,scripts,bin,node_scripts

# 项目标识文件（用于自动检测项目根目录）
stream.server.project.markers=package.json,pom.xml,.git,stream-server.js

# 备用路径（当自动检测失败时使用，多个路径用逗号分隔）
stream.server.fallback.paths=${user.dir},${catalina.base},${catalina.home}

# 健康检查配置
stream.server.health.check.enabled=true
stream.server.health.check.max.retries=15
stream.server.health.check.retry.interval=2000

# 日志配置
stream.server.log.level=INFO
stream.server.log.detailed=true

# 环境配置
stream.server.environment=development
