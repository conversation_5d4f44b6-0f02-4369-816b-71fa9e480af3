package com.building.service.impl;

import com.building.config.StreamServerConfig;
import com.building.util.ProjectPathResolver;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.lang.reflect.Method;

/**
 * 视频流服务实现类测试
 */
public class VideoStreamServiceImplTest {
    
    private VideoStreamServiceImpl videoStreamService;
    
    @Before
    public void setUp() {
        videoStreamService = new VideoStreamServiceImpl();
    }
    
    @Test
    public void testServiceInitialization() {
        // 测试服务初始化
        assertNotNull("视频流服务不应为null", videoStreamService);
    }
    
    @Test
    public void testConfigurationLoading() {
        // 测试配置加载
        StreamServerConfig config = StreamServerConfig.getInstance();
        assertNotNull("配置不应为null", config);
        
        // 验证基本配置
        assertNotNull("服务器URL不应为null", config.getServerUrl());
        assertTrue("连接超时应大于0", config.getConnectionTimeout() > 0);
        assertTrue("读取超时应大于0", config.getReadTimeout() > 0);
        assertNotNull("Node命令不应为null", config.getNodeCommand());
        assertNotNull("脚本名称不应为null", config.getScriptName());
    }
    
    @Test
    public void testPathResolverFunctionality() {
        // 测试路径解析器功能
        ProjectPathResolver pathResolver = new ProjectPathResolver();
        assertNotNull("路径解析器不应为null", pathResolver);
        
        String projectRoot = pathResolver.findProjectRoot();
        assertNotNull("项目根目录不应为null", projectRoot);
        
        // 验证诊断信息
        assertNotNull("搜索路径列表不应为null", pathResolver.getAllSearchPaths());
        assertFalse("搜索路径列表不应为空", pathResolver.getAllSearchPaths().isEmpty());
    }
    
    @Test
    public void testGetProjectRootDirectoryMethod() throws Exception {
        // 使用反射测试私有方法
        Method method = VideoStreamServiceImpl.class.getDeclaredMethod("getProjectRootDirectory");
        method.setAccessible(true);
        
        String projectRoot = (String) method.invoke(videoStreamService);
        assertNotNull("项目根目录不应为null", projectRoot);
        assertFalse("项目根目录不应为空字符串", projectRoot.trim().isEmpty());
    }
    
    @Test
    public void testFindScriptFileMethod() throws Exception {
        // 使用反射测试私有方法
        Method getProjectRootMethod = VideoStreamServiceImpl.class.getDeclaredMethod("getProjectRootDirectory");
        getProjectRootMethod.setAccessible(true);
        String projectRoot = (String) getProjectRootMethod.invoke(videoStreamService);
        
        Method findScriptMethod = VideoStreamServiceImpl.class.getDeclaredMethod("findScriptFile", String.class);
        findScriptMethod.setAccessible(true);
        
        String scriptPath = (String) findScriptMethod.invoke(videoStreamService, projectRoot);
        // 脚本文件可能不存在，这是正常的
        if (scriptPath != null) {
            assertFalse("脚本路径不应为空字符串", scriptPath.trim().isEmpty());
        }
    }
    
    @Test
    public void testLogDiagnosticInfoMethod() throws Exception {
        // 测试诊断信息方法
        Method method = VideoStreamServiceImpl.class.getDeclaredMethod("logDiagnosticInfo");
        method.setAccessible(true);
        
        // 这个方法主要是记录日志，不会抛出异常就算成功
        try {
            method.invoke(videoStreamService);
        } catch (Exception e) {
            fail("诊断信息方法不应抛出异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testBackwardCompatibility() {
        // 测试向后兼容性
        // 验证服务仍然可以正常创建和初始化
        VideoStreamServiceImpl service = new VideoStreamServiceImpl();
        assertNotNull("服务应该能正常创建", service);
        
        // 验证基本方法仍然存在
        assertNotNull("checkStreamServerHealth方法应该存在", 
            getMethod(service.getClass(), "checkStreamServerHealth"));
        assertNotNull("startStreamServer方法应该存在", 
            getMethod(service.getClass(), "startStreamServer"));
        assertNotNull("stopStreamServer方法应该存在", 
            getMethod(service.getClass(), "stopStreamServer"));
    }
    
    private Method getMethod(Class<?> clazz, String methodName) {
        try {
            return clazz.getMethod(methodName);
        } catch (NoSuchMethodException e) {
            return null;
        }
    }
}
