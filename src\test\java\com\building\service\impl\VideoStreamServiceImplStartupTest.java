package com.building.service.impl;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 测试VideoStreamServiceImpl是否能正常启动和初始化
 */
public class VideoStreamServiceImplStartupTest {
    
    @Test
    public void testServiceCanBeInstantiated() {
        // 测试服务是否能正常实例化，不抛出ClassNotFoundException
        try {
            VideoStreamServiceImpl service = new VideoStreamServiceImpl();
            assertNotNull("服务应该能正常创建", service);
            System.out.println("VideoStreamServiceImpl 创建成功");
        } catch (NoClassDefFoundError e) {
            fail("创建VideoStreamServiceImpl时出现NoClassDefFoundError: " + e.getMessage());
        } catch (Exception e) {
            fail("创建VideoStreamServiceImpl时出现异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testConfigurationLoading() {
        // 测试配置是否能正常加载
        try {
            VideoStreamServiceImpl service = new VideoStreamServiceImpl();
            
            // 通过反射测试getProjectRootDirectory方法
            java.lang.reflect.Method method = VideoStreamServiceImpl.class.getDeclaredMethod("getProjectRootDirectory");
            method.setAccessible(true);
            
            String projectRoot = (String) method.invoke(service);
            assertNotNull("项目根目录不应为null", projectRoot);
            assertFalse("项目根目录不应为空字符串", projectRoot.trim().isEmpty());
            
            System.out.println("项目根目录: " + projectRoot);
            
        } catch (Exception e) {
            fail("测试配置加载时出现异常: " + e.getMessage());
        }
    }
    
    @Test
    public void testLoggerInitialization() {
        // 测试日志系统是否正常工作
        try {
            VideoStreamServiceImpl service = new VideoStreamServiceImpl();
            
            // 如果能创建服务实例，说明日志系统工作正常
            System.out.println("日志系统初始化成功");
            
        } catch (Exception e) {
            fail("日志系统初始化失败: " + e.getMessage());
        }
    }
}
