/**
 * EduFusionCenter RTSP视频流转码服务器
 *
 * 功能说明：
 * 1. 接收RTSP视频流并转码为Web可播放的格式（HLS/FLV）
 * 2. 提供RESTful API接口管理视频流
 * 3. 支持多路并发视频流处理
 * 4. 自动重连和错误恢复机制
 * 5. 实时流状态监控和管理
 *
 * 依赖：
 * - express: Web服务器框架
 * - cors: 跨域资源共享中间件
 * - child_process: 用于启动FFmpeg子进程
 * - ws: WebSocket支持（预留）
 * - path: 路径处理工具
 * - fs: 文件系统操作
 *
 * 外部依赖：
 * - FFmpeg: 视频转码工具，需要安装并添加到系统PATH
 */

// 导入必需的Node.js模块
const express = require('express');        // Web服务器框架
const cors = require('cors');              // 跨域资源共享中间件
const { spawn } = require('child_process'); // 子进程管理，用于启动FFmpeg
const WebSocket = require('ws');           // WebSocket支持（预留功能）
const path = require('path');              // 路径处理工具
const fs = require('fs');                  // 文件系统操作

// 创建Express应用实例
const app = express();
// 服务器端口，优先使用环境变量，默认3001
const PORT = process.env.PORT || 3001;

// ==================== 中间件配置 ====================
app.use(cors());                    // 启用CORS，允许跨域请求
app.use(express.json());            // 解析JSON请求体
app.use(express.static('public'));  // 提供静态文件服务（HLS文件等）

// ==================== 全局变量 ====================
// 存储所有活动的视频流进程信息
// Key: streamId (字符串), Value: 流对象 {process, rtspUrl, format, outputUrl, startTime, autoRestart, clients}
const activeStreams = new Map();

// 服务器配置信息
const config = {
    ffmpegPath: 'ffmpeg',                    // FFmpeg可执行文件路径
    outputFormats: ['flv', 'hls', 'm3u8'],  // 支持的输出格式
    defaultFormat: 'hls'                     // 默认输出格式
};

// ==================== 工具函数 ====================

/**
 * 检查FFmpeg是否可用
 *
 * 通过执行 'ffmpeg -version' 命令来验证FFmpeg是否正确安装并可用
 * 这是服务器启动时的必要检查，确保视频转码功能可以正常工作
 *
 * @returns {Promise<boolean>} 如果FFmpeg可用则resolve(true)，否则reject错误信息
 */
function checkFFmpeg() {
    return new Promise((resolve, reject) => {
        // 启动FFmpeg子进程，执行版本检查命令
        const ffmpeg = spawn('ffmpeg', ['-version']);

        // 监听进程退出事件
        ffmpeg.on('close', (code) => {
            if (code === 0) {
                // 退出码为0表示成功
                resolve(true);
            } else {
                // 非0退出码表示FFmpeg不可用或有问题
                reject(new Error('FFmpeg not found'));
            }
        });

        // 监听进程错误事件（如命令不存在）
        ffmpeg.on('error', (err) => {
            reject(err);
        });

        // 设置5秒超时，防止进程挂起
        setTimeout(() => {
            ffmpeg.kill();  // 强制终止进程
            reject(new Error('FFmpeg check timeout'));
        }, 5000);
    });
}

/**
 * 测试RTSP连接是否可用
 *
 * 在正式启动视频流转码之前，先测试RTSP源是否可以正常连接
 * 这可以避免启动无效的转码进程，提高系统稳定性
 *
 * @param {string} rtspUrl - RTSP流地址，格式如: rtsp://username:password@ip:port/path
 * @returns {Promise<boolean>} 连接成功返回true，失败则reject错误信息
 */
function testRTSPConnection(rtspUrl) {
    return new Promise((resolve, reject) => {
        console.log(`测试RTSP连接: ${rtspUrl}`);

        // FFmpeg测试参数配置
        const testArgs = [
            '-i', rtspUrl,      // 输入RTSP流地址
            '-t', '5',          // 只测试5秒，避免长时间占用资源
            '-f', 'null',       // 输出格式为null，不生成实际文件
            '-'                 // 输出到标准输出（丢弃）
        ];

        // 启动FFmpeg测试进程
        const testProcess = spawn('ffmpeg', testArgs);
        let hasError = false;        // 错误标志
        let errorMessage = '';       // 错误信息

        // 监听FFmpeg的错误输出（stderr）
        testProcess.stderr.on('data', (data) => {
            const msg = data.toString();

            // 检查常见的连接错误模式
            if (msg.includes('Connection refused') ||      // 连接被拒绝
                msg.includes('Connection timed out') ||    // 连接超时
                msg.includes('Invalid data found') ||      // 无效数据
                msg.includes('Server returned 401 Unauthorized') ||  // 认证失败
                msg.includes('Server returned 404 Not Found') ||     // 资源不存在
                msg.includes('No route to host')) {        // 网络不可达
                hasError = true;
                errorMessage = msg;
            }
        });

        // 监听进程退出事件
        testProcess.on('close', (code) => {
            if (hasError) {
                reject(new Error(`RTSP连接测试失败: ${errorMessage}`));
            } else {
                // 没有检测到错误，认为连接成功
                resolve(true);
            }
        });

        // 监听进程错误事件
        testProcess.on('error', (err) => {
            reject(err);
        });

        // 设置10秒超时
        setTimeout(() => {
            testProcess.kill();  // 终止测试进程
            if (!hasError) {
                // 如果没有检测到错误且超时，认为连接成功
                // 某些RTSP流可能需要较长时间建立连接
                resolve(true);
            }
        }, 10000);
    });
}

// 启动RTSP转码流
function startRTSPStream(rtspUrl, streamId, format = 'hls') {
    return new Promise(async (resolve, reject) => {
        // 如果流已经存在，先停止它
        if (activeStreams.has(streamId)) {
            stopStream(streamId);
        }

        // 先测试RTSP连接
        try {
            console.log(`开始测试RTSP连接: ${streamId}`);
            await testRTSPConnection(rtspUrl);
            console.log(`RTSP连接测试成功: ${streamId}`);
        } catch (error) {
            console.error(`RTSP连接测试失败: ${streamId}, 错误: ${error.message}`);
            reject(error);
            return;
        }

        let ffmpegArgs;
        let outputUrl;

        if (format === 'flv') {
            // 使用管道输出到HTTP流
            outputUrl = `/live/${streamId}.flv`;
            ffmpegArgs = [
                '-rtsp_transport', 'tcp',
                '-i', rtspUrl,
                '-c:v', 'libx264',
                '-profile:v', 'baseline',
                '-level', '4.1',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-vf', 'scale=1280:720', // 降低分辨率到720p
                '-r', '25',              // 设置帧率
                '-b:v', '1500k',         // 设置视频比特率
                '-maxrate', '2000k',     // 最大比特率
                '-bufsize', '3000k',     // 缓冲区大小
                '-c:a', 'aac',
                '-ar', '44100',
                '-ac', '2',
                '-b:a', '128k',
                '-f', 'flv',
                '-flvflags', 'no_duration_filesize',
                '-fflags', '+genpts',
                '-avoid_negative_ts', 'make_zero',
                'pipe:1'  // 输出到stdout
            ];
        } else if (format === 'hls') {
            // 确保HLS输出目录存在
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (!fs.existsSync(hlsDir)) {
                fs.mkdirSync(hlsDir, { recursive: true });
            }

            outputUrl = `/hls/${streamId}/playlist.m3u8`;
            ffmpegArgs = [
                '-rtsp_transport', 'tcp',  // 使用TCP传输，更稳定
                '-i', rtspUrl,
                '-c:v', 'libx264',
                '-profile:v', 'baseline',
                '-level', '4.1',         // 提高level以支持高分辨率
                '-preset', 'veryfast',
                '-tune', 'zerolatency',
                '-vf', 'scale=1920×1080', // 降低分辨率到720p
                '-r', '30',              // 设置帧率
                '-g', '50',
                '-keyint_min', '25',
                '-sc_threshold', '0',
                '-b:v', '2000k',         // 设置视频比特率
                '-maxrate', '2500k',     // 最大比特率
                '-bufsize', '5000k',     // 缓冲区大小
                '-c:a', 'aac',
                '-ar', '44100',
                '-ac', '2',
                '-b:a', '128k',
                '-f', 'hls',
                '-hls_time', '2',
                '-hls_list_size', '8',
                '-hls_flags', 'delete_segments+append_list+round_durations',
                '-hls_allow_cache', '0',
                '-hls_segment_type', 'mpegts',
                '-hls_segment_filename', path.join(hlsDir, 'segment_%03d.ts'),
                '-force_key_frames', 'expr:gte(t,n_forced*2)',
                '-reconnect', '1',       // 启用重连
                '-reconnect_at_eof', '1', // EOF时重连
                '-reconnect_streamed', '1', // 流式重连
                path.join(hlsDir, 'playlist.m3u8')
            ];
        }

        console.log('启动FFmpeg进程:', ffmpegArgs.join(' '));

        const ffmpeg = spawn('ffmpeg', ffmpegArgs);
        
        ffmpeg.stdout.on('data', (data) => {
            console.log(`FFmpeg stdout: ${data}`);
        });

        ffmpeg.stderr.on('data', (data) => {
            const errorMsg = data.toString();
            console.log(`FFmpeg stderr: ${errorMsg}`);

            // 检查是否是致命错误
            if (errorMsg.includes('Connection refused') ||
                errorMsg.includes('Connection timed out') ||
                errorMsg.includes('Invalid data found') ||
                errorMsg.includes('Server returned 401 Unauthorized') ||
                errorMsg.includes('Server returned 404 Not Found') ||
                errorMsg.includes('No route to host')) {
                console.error(`FFmpeg致命错误检测到: ${errorMsg}`);
                const stream = activeStreams.get(streamId);
                if (stream) {
                    stream.autoRestart = false; // 停止自动重启
                    stream.errorCount = (stream.errorCount || 0) + 1;
                    if (stream.errorCount >= 3) {
                        console.error(`流 ${streamId} 错误次数过多，停止重试`);
                        activeStreams.delete(streamId);
                        return;
                    }
                }
            }
        });

        ffmpeg.on('close', (code) => {
            console.log(`FFmpeg进程退出，代码: ${code}`);
            const stream = activeStreams.get(streamId);
            if (stream && stream.autoRestart !== false) {
                // 增加重试计数和延迟
                stream.retryCount = (stream.retryCount || 0) + 1;
                const maxRetries = 5;
                const retryDelay = Math.min(2000 * stream.retryCount, 30000); // 最大30秒延迟

                if (stream.retryCount <= maxRetries) {
                    console.log(`自动重启流: ${streamId} (第${stream.retryCount}次重试，${retryDelay}ms后重试)`);
                    setTimeout(() => {
                        if (activeStreams.has(streamId)) {
                            console.log(`重新启动FFmpeg进程: ${streamId}`);
                            startRTSPStream(rtspUrl, streamId, format).catch(console.error);
                        }
                    }, retryDelay);
                } else {
                    console.error(`流 ${streamId} 重试次数已达上限，停止重试`);
                    activeStreams.delete(streamId);
                }
            } else {
                activeStreams.delete(streamId);
            }
        });

        ffmpeg.on('error', (err) => {
            console.error('FFmpeg错误:', err);
            const stream = activeStreams.get(streamId);
            if (stream && stream.autoRestart !== false) {
                console.log(`FFmpeg错误，准备重启: ${streamId}`);
                setTimeout(() => {
                    if (activeStreams.has(streamId)) {
                        console.log(`错误恢复，重新启动FFmpeg: ${streamId}`);
                        startRTSPStream(rtspUrl, streamId, format).catch(console.error);
                    }
                }, 5000);
            } else {
                activeStreams.delete(streamId);
                reject(err);
            }
        });

        // 存储流信息
        activeStreams.set(streamId, {
            process: ffmpeg,
            rtspUrl: rtspUrl,
            format: format,
            outputUrl: outputUrl,
            startTime: new Date(),
            autoRestart: true
        });

        // 如果是FLV格式，设置管道处理
        if (format === 'flv') {
            ffmpeg.stdout.on('data', (data) => {
                const stream = activeStreams.get(streamId);
                if (stream && stream.clients) {
                    stream.clients.forEach(client => {
                        if (!client.destroyed) {
                            client.write(data);
                        }
                    });
                }
            });

            const stream = activeStreams.get(streamId);
            if (stream) {
                stream.clients = [];
            }
        }

        // 立即返回响应，不等待FFmpeg完全启动
        // FFmpeg进程已经启动，后台会继续处理
        console.log(`流 ${streamId} 启动成功，进程正在运行`);
        resolve({
            streamId: streamId,
            outputUrl: outputUrl,
            format: format
        });

        // 在后台检查流状态
        setTimeout(() => {
            if (!ffmpeg || ffmpeg.killed) {
                console.error(`流 ${streamId} 启动后进程意外退出`);
                activeStreams.delete(streamId);
            } else {
                console.log(`流 ${streamId} 后台检查：进程运行正常`);
            }
        }, 5000);
    });
}

// 停止流
function stopStream(streamId) {
    const stream = activeStreams.get(streamId);
    if (stream) {
        stream.autoRestart = false;

        if (stream.clients) {
            stream.clients.forEach(client => {
                if (!client.destroyed) {
                    client.end();
                }
            });
        }

        stream.process.kill('SIGTERM');
        activeStreams.delete(streamId);

        // 清理HLS文件
        if (stream.format === 'hls') {
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (fs.existsSync(hlsDir)) {
                fs.rmSync(hlsDir, { recursive: true, force: true });
            }
        }

        return true;
    }
    return false;
}

// 实时FLV流路由
app.get('/live/:streamId.flv', (req, res) => {
    const streamId = req.params.streamId;
    const stream = activeStreams.get(streamId);

    if (!stream || stream.format !== 'flv') {
        return res.status(404).json({ error: '流不存在或格式不匹配' });
    }

    res.writeHead(200, {
        'Content-Type': 'video/x-flv',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Range',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Connection': 'keep-alive'
    });

    if (!stream.clients) {
        stream.clients = [];
    }
    stream.clients.push(res);

    req.on('close', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });

    req.on('error', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });
});

// API路由

// 测试RTSP连接
app.post('/api/rtsp/test', async (req, res) => {
    try {
        const { rtspUrl } = req.body;

        if (!rtspUrl) {
            return res.status(400).json({
                error: '缺少必要参数: rtspUrl'
            });
        }

        await testRTSPConnection(rtspUrl);
        res.json({
            success: true,
            message: 'RTSP连接测试成功',
            data: {
                rtspUrl: rtspUrl,
                status: 'connected'
            }
        });
    } catch (error) {
        console.error('RTSP连接测试失败:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            data: {
                rtspUrl: req.body.rtspUrl,
                status: 'failed'
            }
        });
    }
});

// 启动流
app.post('/api/stream/start', async (req, res) => {
    try {
        const { rtspUrl, streamId, format } = req.body;

        if (!rtspUrl || !streamId) {
            return res.status(400).json({
                error: '缺少必要参数: rtspUrl 和 streamId'
            });
        }

        const result = await startRTSPStream(rtspUrl, streamId, format);
        res.json({
            success: true,
            message: '流启动成功',
            data: result
        });
    } catch (error) {
        console.error('启动流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 停止流
app.post('/api/stream/stop', (req, res) => {
    try {
        const { streamId } = req.body;
        
        if (!streamId) {
            return res.status(400).json({ 
                error: '缺少参数: streamId' 
            });
        }

        const stopped = stopStream(streamId);
        if (stopped) {
            res.json({
                success: true,
                message: '流停止成功'
            });
        } else {
            res.status(404).json({
                success: false,
                error: '流不存在'
            });
        }
    } catch (error) {
        console.error('停止流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取流状态
app.get('/api/stream/status', (req, res) => {
    const streams = Array.from(activeStreams.entries()).map(([id, stream]) => ({
        streamId: id,
        rtspUrl: stream.rtspUrl,
        format: stream.format,
        outputUrl: stream.outputUrl,
        startTime: stream.startTime,
        uptime: Date.now() - stream.startTime.getTime()
    }));

    res.json({
        success: true,
        data: {
            activeStreams: streams.length,
            streams: streams
        }
    });
});

// 停止所有活跃的流
app.post('/api/stream/stop-all', (req, res) => {
    try {
        const streamIds = Array.from(activeStreams.keys());
        let stoppedCount = 0;

        for (const streamId of streamIds) {
            if (stopStream(streamId)) {
                stoppedCount++;
            }
        }

        res.json({
            success: true,
            message: `成功停止 ${stoppedCount} 个活跃流`,
            data: {
                stoppedCount: stoppedCount,
                totalStreams: streamIds.length
            }
        });
    } catch (error) {
        console.error('停止所有流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 清除所有视频缓存
app.post('/api/cache/clear', (req, res) => {
    try {
        const hlsBaseDir = path.join(__dirname, 'public', 'hls');
        let clearedCount = 0;

        // 清除HLS缓存目录
        if (fs.existsSync(hlsBaseDir)) {
            const streamDirs = fs.readdirSync(hlsBaseDir);
            for (const streamDir of streamDirs) {
                const streamPath = path.join(hlsBaseDir, streamDir);
                if (fs.statSync(streamPath).isDirectory()) {
                    fs.rmSync(streamPath, { recursive: true, force: true });
                    clearedCount++;
                    console.log(`清除缓存目录: ${streamPath}`);
                }
            }
        }

        // 清除其他可能的缓存文件
        const publicDir = path.join(__dirname, 'public');
        const tempFiles = ['*.tmp', '*.temp'];

        res.json({
            success: true,
            message: `成功清除 ${clearedCount} 个缓存目录`,
            data: {
                clearedDirectories: clearedCount,
                cacheBasePath: hlsBaseDir
            }
        });
    } catch (error) {
        console.error('清除缓存失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 健康检查
app.get('/api/health', async (req, res) => {
    try {
        await checkFFmpeg();
        res.json({
            success: true,
            message: 'Service is healthy',
            ffmpeg: 'available'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Service unhealthy',
            error: error.message
        });
    }
});

// 启动服务器
app.listen(PORT, '0.0.0.0', async () => {
    console.log(`EduFusionCenter RTSP转码服务器运行在端口 ${PORT}`);
    console.log(`服务器地址: http://localhost:${PORT}`);
    console.log(`服务器地址: http://127.0.0.1:${PORT}`);

    try {
        await checkFFmpeg();
        console.log('✓ FFmpeg 可用');
    } catch (error) {
        console.error('✗ FFmpeg 不可用:', error.message);
        console.log('请确保已安装FFmpeg并添加到系统PATH中');
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('正在关闭服务器...');
    
    for (const [streamId] of activeStreams) {
        stopStream(streamId);
    }
    
    process.exit(0);
});
