package com.building.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * 流服务器配置管理类
 * 负责加载和管理流服务器相关配置
 */
public class StreamServerConfig {
    private static final Logger logger = LoggerFactory.getLogger(StreamServerConfig.class);
    
    private static StreamServerConfig instance;
    private Properties properties;
    
    // 配置文件名
    private static final String DEFAULT_CONFIG_FILE = "stream-server.properties";
    private static final String CONFIG_FILE_PATTERN = "stream-server-%s.properties";
    
    // 环境变量和系统属性键
    private static final String ENV_PROFILE = "STREAM_SERVER_PROFILE";
    private static final String SYS_PROFILE = "stream.server.profile";
    private static final String ENV_PROJECT_ROOT = "STREAM_SERVER_PROJECT_ROOT";
    private static final String SYS_PROJECT_ROOT = "stream.server.project.root";
    
    private StreamServerConfig() {
        loadConfiguration();
    }
    
    /**
     * 获取配置实例（单例模式）
     */
    public static synchronized StreamServerConfig getInstance() {
        if (instance == null) {
            instance = new StreamServerConfig();
        }
        return instance;
    }
    
    /**
     * 加载配置
     */
    private void loadConfiguration() {
        properties = new Properties();
        
        // 1. 加载默认配置
        loadPropertiesFile(DEFAULT_CONFIG_FILE);
        
        // 2. 加载环境特定配置
        String profile = getProfile();
        if (profile != null && !profile.isEmpty()) {
            String envConfigFile = String.format(CONFIG_FILE_PATTERN, profile);
            loadPropertiesFile(envConfigFile);
        }
        
        // 3. 应用系统属性覆盖
        applySystemPropertyOverrides();
        
        logger.info("流服务器配置加载完成，环境: {}", profile);
    }
    
    /**
     * 获取当前环境配置
     */
    private String getProfile() {
        // 优先级：系统属性 > 环境变量 > 配置文件
        String profile = System.getProperty(SYS_PROFILE);
        if (profile == null) {
            profile = System.getenv(ENV_PROFILE);
        }
        if (profile == null) {
            profile = properties.getProperty("stream.server.environment", "development");
        }
        return profile;
    }
    
    /**
     * 加载属性文件
     */
    private void loadPropertiesFile(String fileName) {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(fileName)) {
            if (is != null) {
                properties.load(is);
                logger.debug("成功加载配置文件: {}", fileName);
            } else {
                logger.debug("配置文件不存在: {}", fileName);
            }
        } catch (IOException e) {
            logger.warn("加载配置文件失败: {}, 错误: {}", fileName, e.getMessage());
        }
    }
    
    /**
     * 应用系统属性覆盖
     */
    private void applySystemPropertyOverrides() {
        // 遍历所有系统属性，查找以 "stream.server." 开头的属性
        System.getProperties().forEach((key, value) -> {
            String keyStr = key.toString();
            if (keyStr.startsWith("stream.server.")) {
                properties.setProperty(keyStr, value.toString());
                logger.debug("应用系统属性覆盖: {} = {}", keyStr, value);
            }
        });
    }
    
    // 配置获取方法
    
    public String getServerUrl() {
        return getProperty("stream.server.url", "http://127.0.0.1:3001");
    }
    
    public int getConnectionTimeout() {
        return getIntProperty("stream.server.connection.timeout", 15000);
    }
    
    public int getReadTimeout() {
        return getIntProperty("stream.server.read.timeout", 30000);
    }
    
    public String getNodeCommand() {
        return getProperty("stream.server.node.command", "node");
    }
    
    public String getScriptName() {
        return getProperty("stream.server.script.name", "stream-server.js");
    }
    
    public String getProjectRoot() {
        // 优先级：环境变量 > 系统属性 > 配置文件
        String projectRoot = System.getenv(ENV_PROJECT_ROOT);
        if (projectRoot == null) {
            projectRoot = System.getProperty(SYS_PROJECT_ROOT);
        }
        if (projectRoot == null) {
            projectRoot = getProperty("stream.server.project.root", "");
        }
        return projectRoot.trim();
    }
    
    public List<String> getScriptSearchPaths() {
        String paths = getProperty("stream.server.script.search.paths", ".,scripts,bin,node_scripts");
        return Arrays.asList(paths.split(","));
    }
    
    public List<String> getProjectMarkers() {
        String markers = getProperty("stream.server.project.markers", "package.json,pom.xml,.git,stream-server.js");
        return Arrays.asList(markers.split(","));
    }
    
    public List<String> getFallbackPaths() {
        String paths = getProperty("stream.server.fallback.paths", "${user.dir},${catalina.base},${catalina.home}");
        // 解析系统属性占位符
        paths = resolvePlaceholders(paths);
        return Arrays.asList(paths.split(","));
    }
    
    public boolean isHealthCheckEnabled() {
        return getBooleanProperty("stream.server.health.check.enabled", true);
    }
    
    public int getHealthCheckMaxRetries() {
        return getIntProperty("stream.server.health.check.max.retries", 15);
    }
    
    public int getHealthCheckRetryInterval() {
        return getIntProperty("stream.server.health.check.retry.interval", 2000);
    }
    
    public String getLogLevel() {
        return getProperty("stream.server.log.level", "INFO");
    }
    
    public boolean isDetailedLogging() {
        return getBooleanProperty("stream.server.log.detailed", true);
    }
    
    // 工具方法
    
    private String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    private int getIntProperty(String key, int defaultValue) {
        try {
            return Integer.parseInt(properties.getProperty(key, String.valueOf(defaultValue)));
        } catch (NumberFormatException e) {
            logger.warn("配置项 {} 的值不是有效整数，使用默认值: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    private boolean getBooleanProperty(String key, boolean defaultValue) {
        return Boolean.parseBoolean(properties.getProperty(key, String.valueOf(defaultValue)));
    }
    
    /**
     * 解析占位符（如 ${user.dir}）
     */
    private String resolvePlaceholders(String value) {
        if (value == null) return null;
        
        String result = value;
        // 简单的占位符解析
        if (result.contains("${user.dir}")) {
            result = result.replace("${user.dir}", System.getProperty("user.dir", ""));
        }
        if (result.contains("${catalina.base}")) {
            result = result.replace("${catalina.base}", System.getProperty("catalina.base", ""));
        }
        if (result.contains("${catalina.home}")) {
            result = result.replace("${catalina.home}", System.getProperty("catalina.home", ""));
        }
        return result;
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        loadConfiguration();
        logger.info("配置已重新加载");
    }
}
