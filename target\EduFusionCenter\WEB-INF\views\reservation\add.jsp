<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="添加预约" />
    <jsp:param name="content" value="/WEB-INF/views/reservation/add-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 页面整体样式 */
        body {
            background-color: #f8f9fa;
        }

        /* 页面标题区域样式 */
        .page-header-card {
            border: 1px solid rgba(0,0,0,0.05);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }
        .page-icon-wrapper {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(40,167,69,0.3);
        }
        .page-icon-wrapper i {
            font-size: 1.8rem;
            color: white;
        }
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }
        .page-subtitle {
            font-size: 1rem;
            line-height: 1.5;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 2rem;
        }
        .form-group-section {
            position: relative;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 16px;
            border: 1px solid #e9ecef;
        }
        .form-section-title {
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            color: #6c757d;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.75rem;
        }
        .form-control-lg {
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border-radius: 12px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control-lg:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        .form-select-lg {
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border-radius: 12px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-select-lg:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        /* 预约类型选择器 */
        .reservation-type-selector .type-option {
            position: relative;
        }
        .type-option input[type='radio'] {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        .type-option-label {
            display: block;
            padding: 1.5rem;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            text-decoration: none;
            color: inherit;
        }
        .type-option-label:hover {
            border-color: #007bff;
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }
        .type-option input[type='radio']:checked + .type-option-label {
            border-color: #007bff;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        .type-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: rgba(0,123,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .type-option input[type='radio']:checked + .type-option-label .type-icon {
            background: rgba(255,255,255,0.2);
        }
        .type-icon i {
            font-size: 1.5rem;
            color: #007bff;
        }
        .type-option input[type='radio']:checked + .type-option-label .type-icon i {
            color: white;
        }
        .type-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .type-description {
            font-size: 0.9rem;
            opacity: 0.8;
            margin: 0;
        }

        /* 表单操作按钮 */
        .form-actions {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #e9ecef;
        }
        .btn-lg {
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-lg:hover {
            transform: translateY(-2px);
        }
    " />
</jsp:include>